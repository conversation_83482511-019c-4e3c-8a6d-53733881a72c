"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token, user, account, profile }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token.email,\n                userEmail: user?.email\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token.id = user.id || \"temp-id\";\n                token.role = \"CLIENT\"; // Default role\n                token.status = \"ACTIVE\";\n                token.language = \"ar\";\n                token.firstName = user.name?.split(\" \")[0] || \"User\";\n                token.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token.avatar = user.image;\n                token.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token.id,\n                    role: token.role,\n                    hasCompletedOnboarding: token.hasCompletedOnboarding\n                });\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token.hasCompletedOnboarding\n            });\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.language = token.language;\n                session.user.firstName = token.firstName;\n                session.user.lastName = token.lastName;\n                session.user.avatar = token.avatar;\n                session.user.hasCompletedOnboarding = token.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl, token }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl,\n                    token\n                });\n                // Check if user has completed onboarding\n                if (token?.hasCompletedOnboarding) {\n                    console.log(\"✅ User completed onboarding, redirecting to dashboard\");\n                    // Redirect to appropriate dashboard based on role\n                    switch(token.role){\n                        case \"ADMIN\":\n                            console.log(\"\\uD83D\\uDD04 Redirecting ADMIN to dashboard\");\n                            return \"http://localhost:3001/dashboard\";\n                        case \"EXPERT\":\n                            console.log(\"\\uD83D\\uDD04 Redirecting EXPERT to dashboard\");\n                            return \"http://localhost:3002/dashboard\";\n                        case \"CLIENT\":\n                            console.log(\"\\uD83D\\uDD04 Redirecting CLIENT to landing page with completion\");\n                            return `${baseUrl}/?auth=success&role=client&onboarding=complete`;\n                        default:\n                            console.log(\"\\uD83D\\uDD04 Redirecting unknown role to landing page\");\n                            return `${baseUrl}/?auth=success&onboarding=complete`;\n                    }\n                }\n                // Redirect ALL new users to AI onboarding (MANDATORY)\n                console.log(\"\\uD83E\\uDD16 Redirecting to AI onboarding for new user\");\n                return `${baseUrl}/ai-onboarding`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token }) {\n            console.log(`👋 User ${token?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();