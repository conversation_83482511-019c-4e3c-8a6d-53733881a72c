import React from 'react';
import { motion } from 'framer-motion';

interface OnboardingProgressProps {
  currentStep: 'role_selection' | 'data_collection' | 'ai_introduction' | 'chat_conversation' | 'completion';
  selectedRole: 'CLIENT' | 'EXPERT' | 'BUSINESS' | null;
  completionRate: number;
}

interface StepIndicatorProps {
  step: number;
  title: string;
  isActive: boolean;
  isCompleted: boolean;
  icon: React.ReactNode;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  step,
  title,
  isActive,
  isCompleted,
  icon
}) => {
  return (
    <div className="flex items-center">
      {/* Step Circle */}
      <div className="relative">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ 
            scale: isActive ? 1.1 : 1,
            backgroundColor: isCompleted 
              ? '#10B981' 
              : isActive 
                ? '#3B82F6' 
                : '#E5E7EB'
          }}
          transition={{ duration: 0.3 }}
          className={`
            w-10 h-10 rounded-full flex items-center justify-center
            ${isCompleted 
              ? 'bg-green-500 text-white' 
              : isActive 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
            }
            shadow-lg
          `}
        >
          {isCompleted ? (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          ) : (
            icon
          )}
        </motion.div>

        {/* Active pulse effect */}
        {isActive && (
          <div className="absolute inset-0 rounded-full border-2 border-blue-300 animate-ping opacity-30"></div>
        )}
      </div>

      {/* Step Title */}
      <div className="mr-3 rtl:ml-3 rtl:mr-0">
        <p className={`
          text-sm font-medium
          ${isActive 
            ? 'text-blue-600 dark:text-blue-400' 
            : isCompleted 
              ? 'text-green-600 dark:text-green-400'
              : 'text-gray-500 dark:text-gray-400'
          }
        `}>
          {title}
        </p>
      </div>
    </div>
  );
};

const OnboardingProgress: React.FC<OnboardingProgressProps> = ({
  currentStep,
  selectedRole,
  completionRate
}) => {
  const steps = [
    {
      key: 'role_selection',
      title: 'اختيار الدور',
      icon: <span className="text-sm font-bold">1</span>
    },
    {
      key: 'data_collection',
      title: 'المعلومات الشخصية',
      icon: <span className="text-sm font-bold">2</span>
    },
    {
      key: 'ai_introduction',
      title: 'مقدمة الذكاء الاصطناعي',
      icon: <span className="text-sm font-bold">3</span>
    },
    {
      key: 'chat_conversation',
      title: 'المحادثة التفاعلية',
      icon: <span className="text-sm font-bold">4</span>
    },
    {
      key: 'completion',
      title: 'إكمال التسجيل',
      icon: <span className="text-sm font-bold">5</span>
    }
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.key === currentStep);
  };

  const currentStepIndex = getCurrentStepIndex();

  return (
    <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Progress Steps */}
          <div className="flex items-center space-x-8 rtl:space-x-reverse">
            {steps.map((step, index) => (
              <React.Fragment key={step.key}>
                <StepIndicator
                  step={index + 1}
                  title={step.title}
                  isActive={index === currentStepIndex}
                  isCompleted={index < currentStepIndex}
                  icon={step.icon}
                />
                
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700 mx-4">
                    <motion.div
                      initial={{ width: '0%' }}
                      animate={{ 
                        width: index < currentStepIndex ? '100%' : '0%'
                      }}
                      transition={{ duration: 0.5 }}
                      className="h-full bg-green-500"
                    />
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Role Badge & Progress */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Selected Role Badge */}
            {selectedRole && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center space-x-2 rtl:space-x-reverse bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full text-sm"
              >
                <span>
                  {selectedRole === 'EXPERT' ? '👨‍💼' : selectedRole === 'CLIENT' ? '🏢' : '🏭'}
                </span>
                <span className="font-medium">
                  {selectedRole === 'EXPERT' ? 'خبير' : selectedRole === 'CLIENT' ? 'عميل' : 'شركة'}
                </span>
              </motion.div>
            )}

            {/* Completion Progress */}
            {currentStep === 'chat_conversation' && completionRate > 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center space-x-2 rtl:space-x-reverse"
              >
                <div className="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: '0%' }}
                    animate={{ width: `${completionRate * 100}%` }}
                    transition={{ duration: 0.5 }}
                    className="h-full bg-gradient-to-r from-green-400 to-green-500"
                  />
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {Math.round(completionRate * 100)}%
                </span>
              </motion.div>
            )}
          </div>
        </div>

        {/* Step Description */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-3"
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {currentStep === 'role_selection' && 'اختر دورك في المنصة لنتمكن من تخصيص تجربتك'}
            {currentStep === 'data_collection' && 'أكمل معلوماتك الشخصية ومعلومات الموقع'}
            {currentStep === 'ai_introduction' && 'تعرف على المساعد الذكي وما يمكنه فعله لك'}
            {currentStep === 'chat_conversation' && 'تفاعل مع المساعد الذكي لإعداد ملفك الشخصي'}
            {currentStep === 'completion' && 'تهانينا! تم إكمال عملية التسجيل بنجاح'}
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default OnboardingProgress;
