import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'next-i18next';

interface DataCollectionFormProps {
  onSubmit: (data: any) => void;
  isLoading?: boolean;
  selectedRole: 'EXPERT' | 'CLIENT' | 'BUSINESS';
  initialData?: any;
}

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  location: {
    city: string;
    governorate: string;
  };
  servicePreferences?: string[];
  projectTypes?: string[];
  businessInfo?: {
    companyName: string;
    industry: string;
    size: string;
  };
}

export function DataCollectionForm({
  onSubmit,
  isLoading = false,
  selectedRole,
  initialData
}: DataCollectionFormProps) {
  const { t } = useTranslation('common');
  
  const [formData, setFormData] = useState<FormData>({
    firstName: initialData?.firstName || '',
    lastName: initialData?.lastName || '',
    email: initialData?.email || '',
    phoneNumber: initialData?.phoneNumber || '',
    location: {
      city: initialData?.location?.city || '',
      governorate: initialData?.location?.governorate || '',
    },
    servicePreferences: initialData?.servicePreferences || [],
    projectTypes: initialData?.projectTypes || [],
    businessInfo: initialData?.businessInfo || {
      companyName: '',
      industry: '',
      size: '',
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Syrian governorates and major cities
  const SYRIAN_LOCATIONS = {
    'دمشق': ['دمشق', 'داريا', 'دوما', 'جرمانا', 'قدسيا'],
    'ريف دمشق': ['الزبداني', 'قطنا', 'التل', 'يبرود', 'النبك'],
    'حلب': ['حلب', 'منبج', 'عفرين', 'اعزاز', 'الباب'],
    'حمص': ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة'],
    'حماة': ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية'],
    'اللاذقية': ['اللاذقية', 'جبلة', 'القرداحة', 'الحفة'],
    'طرطوس': ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش'],
    'إدلب': ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان'],
    'الحسكة': ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية'],
    'دير الزور': ['دير الزور', 'الميادين', 'البوكمال', 'الرقة'],
    'الرقة': ['الرقة', 'تل أبيض', 'الثورة'],
    'درعا': ['درعا', 'إزرع', 'الصنمين', 'نوى'],
    'السويداء': ['السويداء', 'شهبا', 'صلخد', 'القريا'],
    'القنيطرة': ['القنيطرة', 'فيق', 'خان أرنبة']
  };

  const SERVICE_CATEGORIES = {
    EXPERT: [
      'تطوير المواقع',
      'التصميم الجرافيكي',
      'التسويق الرقمي',
      'الترجمة',
      'المحاسبة',
      'الاستشارات القانونية',
      'التصوير',
      'الكتابة والتحرير',
      'البرمجة',
      'التدريس',
      'الخدمات الهندسية',
      'الخدمات الطبية',
      'الكهرباء والصيانة',
      'السباكة',
      'النجارة',
      'البناء والتشييد'
    ],
    CLIENT: [
      'مشاريع تقنية',
      'تصميم وإبداع',
      'تسويق ومبيعات',
      'خدمات تجارية',
      'استشارات',
      'خدمات منزلية',
      'خدمات طبية',
      'تعليم وتدريب',
      'خدمات قانونية',
      'خدمات مالية'
    ]
  };

  const BUSINESS_INDUSTRIES = [
    'التكنولوجيا',
    'التجارة الإلكترونية',
    'التعليم',
    'الصحة',
    'العقارات',
    'السياحة',
    'الصناعة',
    'الزراعة',
    'الخدمات المالية',
    'الإعلام والاتصالات',
    'النقل واللوجستيات',
    'الطاقة',
    'أخرى'
  ];

  const BUSINESS_SIZES = [
    'شركة ناشئة (1-10 موظفين)',
    'شركة صغيرة (11-50 موظف)',
    'شركة متوسطة (51-200 موظف)',
    'شركة كبيرة (200+ موظف)'
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'الاسم الأول مطلوب';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'الاسم الأخير مطلوب';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'رقم الهاتف مطلوب';
    } else if (!/^(\+963|0)?[0-9]{8,9}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'رقم الهاتف غير صحيح (يجب أن يكون رقم سوري صحيح)';
    }

    if (!formData.location.governorate) {
      newErrors.governorate = 'المحافظة مطلوبة';
    }

    if (!formData.location.city) {
      newErrors.city = 'المدينة مطلوبة';
    }

    // Role-specific validation
    if (selectedRole === 'EXPERT' && (!formData.servicePreferences || formData.servicePreferences.length === 0)) {
      newErrors.servicePreferences = 'يجب اختيار مجال خبرة واحد على الأقل';
    }

    if (selectedRole === 'CLIENT' && (!formData.projectTypes || formData.projectTypes.length === 0)) {
      newErrors.projectTypes = 'يجب اختيار نوع مشروع واحد على الأقل';
    }

    if (selectedRole === 'BUSINESS') {
      if (!formData.businessInfo?.companyName.trim()) {
        newErrors.companyName = 'اسم الشركة مطلوب';
      }
      if (!formData.businessInfo?.industry) {
        newErrors.industry = 'مجال الشركة مطلوب';
      }
      if (!formData.businessInfo?.size) {
        newErrors.size = 'حجم الشركة مطلوب';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleLocationChange = (field: 'governorate' | 'city', value: string) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value,
        // Reset city when governorate changes
        ...(field === 'governorate' && { city: '' })
      }
    }));
    
    // Clear errors
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleBusinessInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      businessInfo: {
        ...prev.businessInfo!,
        [field]: value
      }
    }));
    
    // Clear error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleArrayFieldChange = (field: 'servicePreferences' | 'projectTypes', value: string) => {
    setFormData(prev => {
      const currentArray = prev[field] || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      
      return {
        ...prev,
        [field]: newArray
      };
    });
    
    // Clear error
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    
    if (cleaned.startsWith('963')) {
      const number = cleaned.substring(3);
      return `+963 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    } else if (cleaned.startsWith('0')) {
      const number = cleaned.substring(1);
      return `0${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
    }
    
    return phone;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="max-w-4xl mx-auto p-6"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          إكمال المعلومات الشخصية
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {selectedRole === 'EXPERT' && 'أكمل معلوماتك لإنشاء ملف خبير احترافي'}
          {selectedRole === 'CLIENT' && 'أكمل معلوماتك للعثور على أفضل الخبراء'}
          {selectedRole === 'BUSINESS' && 'أكمل معلومات شركتك لإدارة المشاريع بكفاءة'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            المعلومات الشخصية
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الاسم الأول *
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.firstName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="أدخل الاسم الأول"
                disabled={isLoading}
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الاسم الأخير *
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.lastName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="أدخل الاسم الأخير"
                disabled={isLoading}
              />
              {errors.lastName && (
                <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                البريد الإلكتروني *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                رقم الهاتف *
              </label>
              <input
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => {
                  const formatted = formatPhoneNumber(e.target.value);
                  handleInputChange('phoneNumber', formatted);
                }}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="+963 XX XXX XXXX أو 0XX XXX XXXX"
                disabled={isLoading}
              />
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
              )}
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                أدخل رقم هاتف سوري صحيح
              </p>
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
            معلومات الموقع
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Governorate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المحافظة *
              </label>
              <select
                value={formData.location.governorate}
                onChange={(e) => handleLocationChange('governorate', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.governorate ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading}
              >
                <option value="">اختر المحافظة</option>
                {Object.keys(SYRIAN_LOCATIONS).map((governorate) => (
                  <option key={governorate} value={governorate}>
                    {governorate}
                  </option>
                ))}
              </select>
              {errors.governorate && (
                <p className="mt-1 text-sm text-red-600">{errors.governorate}</p>
              )}
            </div>

            {/* City */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المدينة *
              </label>
              <select
                value={formData.location.city}
                onChange={(e) => handleLocationChange('city', e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.city ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading || !formData.location.governorate}
              >
                <option value="">اختر المدينة</option>
                {formData.location.governorate &&
                  SYRIAN_LOCATIONS[formData.location.governorate as keyof typeof SYRIAN_LOCATIONS]?.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))
                }
              </select>
              {errors.city && (
                <p className="mt-1 text-sm text-red-600">{errors.city}</p>
              )}
              {!formData.location.governorate && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  اختر المحافظة أولاً
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Role-specific sections */}
        {selectedRole === 'EXPERT' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              مجالات الخبرة
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                اختر مجالات خبرتك (يمكن اختيار أكثر من مجال) *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {SERVICE_CATEGORIES.EXPERT.map((service) => (
                  <label
                    key={service}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      formData.servicePreferences?.includes(service)
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.servicePreferences?.includes(service) || false}
                      onChange={() => handleArrayFieldChange('servicePreferences', service)}
                      className="sr-only"
                      disabled={isLoading}
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {service}
                    </span>
                  </label>
                ))}
              </div>
              {errors.servicePreferences && (
                <p className="mt-2 text-sm text-red-600">{errors.servicePreferences}</p>
              )}
            </div>
          </div>
        )}

        {selectedRole === 'CLIENT' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              أنواع المشاريع
            </h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                ما نوع المشاريع التي تحتاج إليها؟ (يمكن اختيار أكثر من نوع) *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {SERVICE_CATEGORIES.CLIENT.map((projectType) => (
                  <label
                    key={projectType}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200 ${
                      formData.projectTypes?.includes(projectType)
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-primary-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={formData.projectTypes?.includes(projectType) || false}
                      onChange={() => handleArrayFieldChange('projectTypes', projectType)}
                      className="sr-only"
                      disabled={isLoading}
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {projectType}
                    </span>
                  </label>
                ))}
              </div>
              {errors.projectTypes && (
                <p className="mt-2 text-sm text-red-600">{errors.projectTypes}</p>
              )}
            </div>
          </div>
        )}

        {selectedRole === 'BUSINESS' && (
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              معلومات الشركة
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Company Name */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم الشركة *
                </label>
                <input
                  type="text"
                  value={formData.businessInfo?.companyName || ''}
                  onChange={(e) => handleBusinessInfoChange('companyName', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.companyName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="أدخل اسم الشركة"
                  disabled={isLoading}
                />
                {errors.companyName && (
                  <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>
                )}
              </div>

              {/* Industry */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مجال الشركة *
                </label>
                <select
                  value={formData.businessInfo?.industry || ''}
                  onChange={(e) => handleBusinessInfoChange('industry', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.industry ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                >
                  <option value="">اختر مجال الشركة</option>
                  {BUSINESS_INDUSTRIES.map((industry) => (
                    <option key={industry} value={industry}>
                      {industry}
                    </option>
                  ))}
                </select>
                {errors.industry && (
                  <p className="mt-1 text-sm text-red-600">{errors.industry}</p>
                )}
              </div>

              {/* Company Size */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  حجم الشركة *
                </label>
                <select
                  value={formData.businessInfo?.size || ''}
                  onChange={(e) => handleBusinessInfoChange('size', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                    errors.size ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isLoading}
                >
                  <option value="">اختر حجم الشركة</option>
                  {BUSINESS_SIZES.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
                {errors.size && (
                  <p className="mt-1 text-sm text-red-600">{errors.size}</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-center">
          <motion.button
            type="submit"
            disabled={isLoading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white font-semibold rounded-xl hover:from-primary-700 hover:to-primary-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </div>
            ) : (
              'متابعة إلى المحادثة الذكية'
            )}
          </motion.button>
        </div>
      </form>
    </motion.div>
  );
}
