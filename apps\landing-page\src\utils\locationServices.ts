// Syrian Location Services Utility

export interface SyrianLocation {
  governorate: string;
  city: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface LocationDistance {
  distance: number; // in kilometers
  travelTime: number; // in minutes
  cost: number; // estimated travel cost in SYP
}

// Syrian governorates with major cities and approximate coordinates
export const SYRIAN_LOCATIONS: Record<string, { cities: string[]; coordinates: { lat: number; lng: number } }> = {
  'دمشق': {
    cities: ['دمشق', 'داريا', 'دوما', 'جرمانا', 'قدسيا'],
    coordinates: { lat: 33.5138, lng: 36.2765 }
  },
  'ريف دمشق': {
    cities: ['الزبداني', 'قطنا', 'التل', 'يبرود', 'النبك'],
    coordinates: { lat: 33.6000, lng: 36.3000 }
  },
  'حلب': {
    cities: ['حلب', 'منبج', 'عفرين', 'اعزاز', 'الباب'],
    coordinates: { lat: 36.2021, lng: 37.1343 }
  },
  'حمص': {
    cities: ['حمص', 'تدمر', 'القريتين', 'الرستن', 'تلبيسة'],
    coordinates: { lat: 34.7394, lng: 36.7163 }
  },
  'حماة': {
    cities: ['حماة', 'سلمية', 'مصياف', 'محردة', 'السقيلبية'],
    coordinates: { lat: 35.1320, lng: 36.7500 }
  },
  'اللاذقية': {
    cities: ['اللاذقية', 'جبلة', 'القرداحة', 'الحفة'],
    coordinates: { lat: 35.5376, lng: 35.7831 }
  },
  'طرطوس': {
    cities: ['طرطوس', 'بانياس', 'صافيتا', 'دريكيش'],
    coordinates: { lat: 34.8886, lng: 35.8854 }
  },
  'إدلب': {
    cities: ['إدلب', 'جسر الشغور', 'أريحا', 'معرة النعمان'],
    coordinates: { lat: 35.9312, lng: 36.6333 }
  },
  'الحسكة': {
    cities: ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية'],
    coordinates: { lat: 36.5004, lng: 40.7478 }
  },
  'دير الزور': {
    cities: ['دير الزور', 'الميادين', 'البوكمال'],
    coordinates: { lat: 35.3444, lng: 40.1467 }
  },
  'الرقة': {
    cities: ['الرقة', 'تل أبيض', 'الثورة'],
    coordinates: { lat: 35.9500, lng: 39.0167 }
  },
  'درعا': {
    cities: ['درعا', 'إزرع', 'الصنمين', 'نوى'],
    coordinates: { lat: 32.6189, lng: 36.1021 }
  },
  'السويداء': {
    cities: ['السويداء', 'شهبا', 'صلخد', 'القريا'],
    coordinates: { lat: 32.7094, lng: 36.5694 }
  },
  'القنيطرة': {
    cities: ['القنيطرة', 'فيق', 'خان أرنبة'],
    coordinates: { lat: 33.1267, lng: 35.8233 }
  }
};

// Service categories that support physical delivery
export const PHYSICAL_SERVICE_CATEGORIES = [
  'الكهرباء والصيانة',
  'السباكة',
  'النجارة',
  'البناء والتشييد',
  'الخدمات الطبية',
  'التصوير',
  'التدريس'
];

// Digital service categories (no location restrictions)
export const DIGITAL_SERVICE_CATEGORIES = [
  'تطوير المواقع',
  'التصميم الجرافيكي',
  'التسويق الرقمي',
  'الترجمة',
  'المحاسبة',
  'الاستشارات القانونية',
  'الكتابة والتحرير',
  'البرمجة'
];

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculate travel cost based on distance and service type
 */
export function calculateTravelCost(distance: number, serviceType: string): number {
  // Base rates in Syrian Pounds (SYP)
  const baseCostPerKm = 500; // 500 SYP per km
  const minimumCost = 2000; // Minimum 2000 SYP
  
  // Service type multipliers
  const serviceMultipliers: Record<string, number> = {
    'الكهرباء والصيانة': 1.2,
    'السباكة': 1.2,
    'النجارة': 1.5,
    'البناء والتشييد': 1.8,
    'الخدمات الطبية': 2.0,
    'التصوير': 1.3,
    'التدريس': 1.0
  };
  
  const multiplier = serviceMultipliers[serviceType] || 1.0;
  const calculatedCost = distance * baseCostPerKm * multiplier;
  
  return Math.max(calculatedCost, minimumCost);
}

/**
 * Estimate travel time based on distance
 */
export function estimateTravelTime(distance: number): number {
  // Average speed in Syrian cities: 30 km/h
  const averageSpeed = 30;
  return Math.round((distance / averageSpeed) * 60); // Convert to minutes
}

/**
 * Get location details by governorate and city
 */
export function getLocationDetails(governorate: string, city: string): SyrianLocation | null {
  const govData = SYRIAN_LOCATIONS[governorate];
  if (!govData || !govData.cities.includes(city)) {
    return null;
  }
  
  return {
    governorate,
    city,
    coordinates: {
      latitude: govData.coordinates.lat,
      longitude: govData.coordinates.lng
    }
  };
}

/**
 * Calculate service delivery details between two locations
 */
export function calculateServiceDelivery(
  expertLocation: SyrianLocation,
  clientLocation: SyrianLocation,
  serviceType: string
): LocationDistance | null {
  if (!expertLocation.coordinates || !clientLocation.coordinates) {
    return null;
  }
  
  const distance = calculateDistance(
    expertLocation.coordinates.latitude,
    expertLocation.coordinates.longitude,
    clientLocation.coordinates.latitude,
    clientLocation.coordinates.longitude
  );
  
  const travelTime = estimateTravelTime(distance);
  const cost = calculateTravelCost(distance, serviceType);
  
  return {
    distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
    travelTime,
    cost
  };
}

/**
 * Check if a service requires physical presence
 */
export function isPhysicalService(serviceCategory: string): boolean {
  return PHYSICAL_SERVICE_CATEGORIES.includes(serviceCategory);
}

/**
 * Get all cities for a governorate
 */
export function getCitiesForGovernorate(governorate: string): string[] {
  return SYRIAN_LOCATIONS[governorate]?.cities || [];
}

/**
 * Get all governorates
 */
export function getAllGovernorates(): string[] {
  return Object.keys(SYRIAN_LOCATIONS);
}

/**
 * Find nearby experts within a radius
 */
export function findNearbyLocations(
  centerLocation: SyrianLocation,
  radiusKm: number
): SyrianLocation[] {
  if (!centerLocation.coordinates) {
    return [];
  }
  
  const nearbyLocations: SyrianLocation[] = [];
  
  Object.entries(SYRIAN_LOCATIONS).forEach(([governorate, data]) => {
    const distance = calculateDistance(
      centerLocation.coordinates!.latitude,
      centerLocation.coordinates!.longitude,
      data.coordinates.lat,
      data.coordinates.lng
    );
    
    if (distance <= radiusKm) {
      data.cities.forEach(city => {
        nearbyLocations.push({
          governorate,
          city,
          coordinates: {
            latitude: data.coordinates.lat,
            longitude: data.coordinates.lng
          }
        });
      });
    }
  });
  
  return nearbyLocations;
}

/**
 * Format location for display
 */
export function formatLocation(location: SyrianLocation): string {
  return `${location.city}, ${location.governorate}`;
}

/**
 * Validate Syrian phone number
 */
export function validateSyrianPhone(phone: string): boolean {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check for valid Syrian phone number patterns
  // +963XXXXXXXXX or 0XXXXXXXXX
  return /^(963|0)?[0-9]{8,9}$/.test(cleaned);
}

/**
 * Format Syrian phone number
 */
export function formatSyrianPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.startsWith('963')) {
    const number = cleaned.substring(3);
    return `+963 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
  } else if (cleaned.startsWith('0')) {
    const number = cleaned.substring(1);
    return `0${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}`;
  }
  
  return phone;
}
