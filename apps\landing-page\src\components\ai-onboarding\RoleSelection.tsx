import React from 'react';
import { motion } from 'framer-motion';

interface RoleSelectionProps {
  onRoleSelect: (role: 'CLIENT' | 'EXPERT' | 'BUSINESS') => void;
  selectedRole: 'CLIENT' | 'EXPERT' | 'BUSINESS' | null;
  isLoading: boolean;
}

interface RoleCardProps {
  icon: string;
  title: string;
  description: string;
  role: 'CLIENT' | 'EXPERT' | 'BUSINESS';
  selected: boolean;
  onSelect: (role: 'CLIENT' | 'EXPERT' | 'BUSINESS') => void;
  disabled: boolean;
}

const RoleCard: React.FC<RoleCardProps> = ({
  icon,
  title,
  description,
  role,
  selected,
  onSelect,
  disabled
}) => {
  return (
    <motion.div
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      className={`
        relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300
        ${selected 
          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
          : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-primary-300 dark:hover:border-primary-600'
        }
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        backdrop-blur-sm
      `}
      onClick={() => !disabled && onSelect(role)}
    >
      {/* Glass morphism effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl" />
      
      {/* Selection indicator */}
      {selected && (
        <div className="absolute top-4 right-4">
          <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      )}

      {/* Icon */}
      <div className="text-4xl mb-4">{icon}</div>

      {/* Content */}
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
        {title}
      </h3>
      <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
        {description}
      </p>
    </motion.div>
  );
};

const RoleSelection: React.FC<RoleSelectionProps> = ({
  onRoleSelect,
  selectedRole,
  isLoading
}) => {
  return (
    <div className="max-w-4xl mx-auto text-center">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-12"
      >
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full mb-6">
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </div>
        
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          مرحباً بك في فريلا سوريا
        </h1>
        
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto leading-relaxed">
          لنبدأ رحلتك معنا! اختر دورك في المنصة لنتمكن من تخصيص تجربتك بأفضل شكل ممكن
        </p>
      </motion.div>

      {/* Role Cards */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="grid md:grid-cols-3 gap-6 mb-8"
      >
        <RoleCard
          icon="👨‍💼"
          title="أنا خبير"
          description="أقدم خدمات احترافية وأبحث عن مشاريع تناسب مهاراتي وخبراتي"
          role="EXPERT"
          selected={selectedRole === 'EXPERT'}
          onSelect={onRoleSelect}
          disabled={isLoading}
        />

        <RoleCard
          icon="🏢"
          title="أنا عميل"
          description="أبحث عن خبراء محترفين لتنفيذ مشاريعي وتحقيق أهدافي التجارية"
          role="CLIENT"
          selected={selectedRole === 'CLIENT'}
          onSelect={onRoleSelect}
          disabled={isLoading}
        />

        <RoleCard
          icon="🏭"
          title="أنا شركة"
          description="أمثل شركة أو مؤسسة وأحتاج لإدارة مشاريع متعددة وفرق عمل"
          role="BUSINESS"
          selected={selectedRole === 'BUSINESS'}
          onSelect={onRoleSelect}
          disabled={isLoading}
        />
      </motion.div>

      {/* Continue Button */}
      {selectedRole && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <button
            onClick={() => onRoleSelect(selectedRole)}
            disabled={isLoading}
            className="
              px-8 py-4 bg-gradient-to-r from-primary-600 to-primary-700 
              text-white font-semibold rounded-xl
              hover:from-primary-700 hover:to-primary-800
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-all duration-300 transform hover:scale-105
              shadow-lg hover:shadow-xl
              backdrop-blur-sm
            "
          >
            {isLoading ? (
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري التحميل...</span>
              </div>
            ) : (
              'متابعة'
            )}
          </button>
        </motion.div>
      )}

      {/* Features Preview */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 text-center"
      >
        <div className="p-4">
          <div className="text-3xl mb-2">🤖</div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-1">مساعد ذكي</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            سيساعدك الذكاء الاصطناعي في إعداد ملفك الشخصي
          </p>
        </div>
        
        <div className="p-4">
          <div className="text-3xl mb-2">⚡</div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-1">سريع وسهل</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            عملية تسجيل سريعة لا تستغرق أكثر من 5 دقائق
          </p>
        </div>
        
        <div className="p-4">
          <div className="text-3xl mb-2">🎯</div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-1">مخصص لك</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            تجربة مصممة خصيصاً لاحتياجاتك وأهدافك
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default RoleSelection;
